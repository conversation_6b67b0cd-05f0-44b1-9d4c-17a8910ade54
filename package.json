{"name": "affeasy-monorepo", "private": true, "license": "AGPL-3.0-or-later", "scripts": {"build": "turbo build", "dev": "turbo dev", "lint": "turbo lint", "clean": "turbo clean", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "publish-tw": "turbo build --filter='@dub/tailwind-config' && cd packages/tailwind-config && npm publish && cd ../../", "publish-ui": "turbo build --filter='@dub/ui' && cd packages/ui && npm publish && cd ../../", "publish-utils": "turbo build --filter='@dub/utils' && cd packages/utils && npm publish && cd ../../", "script": "echo 'Run this script in apps/web'"}, "devDependencies": {"@dub/tailwind-config": "workspace:*", "eslint": "^8.48.0", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tsconfig": "workspace:*", "turbo": "^1.12.5"}, "packageManager": "pnpm@8.6.10"}