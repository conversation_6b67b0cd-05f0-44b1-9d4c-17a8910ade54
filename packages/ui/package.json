{"name": "@dub/ui", "description": "UI components for Dub.co", "version": "0.0.65", "sideEffects": false, "main": "./dist/index.js", "module": "./dist/index.mjs", "types": "./dist/index.d.ts", "files": ["dist/**"], "scripts": {"build": "tsup", "lint": "eslint src/", "dev": "tsup --watch", "check-types": "tsc --noEmit"}, "peerDependencies": {"@vercel/analytics": "^1.0.0", "next": "13.5.4", "react": "^18.2.0"}, "devDependencies": {"@dub/tailwind-config": "workspace:*", "@dub/utils": "workspace:*", "@types/js-cookie": "^3.0.6", "@types/react": "^18.2.47", "autoprefixer": "^10.4.16", "next": "13.5.4", "postcss": "^8.4.31", "react": "^18.2.0", "tailwindcss": "^3.3.3", "tsconfig": "workspace:*", "tsup": "^6.1.3", "typescript": "^5.1.6"}, "dependencies": {"@radix-ui/react-accordion": "^1.0.1", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-navigation-menu": "^1.1.2", "@radix-ui/react-popover": "1.0.7", "@radix-ui/react-radio-group": "^1.1.3", "@radix-ui/react-slot": "1.0.1", "@radix-ui/react-switch": "^1.0.1", "@radix-ui/react-tooltip": "^1.0.7", "class-variance-authority": "^0.7.0", "cmdk": "^0.2.0", "framer-motion": "^10.16.16", "js-cookie": "^3.0.5", "linkify-react": "^4.1.3", "lucide-react": "^0.284.0", "sonner": "^0.5.0", "swr": "^2.1.5", "use-debounce": "^8.0.4", "vaul": "^0.8.0"}, "author": "<PERSON> <<EMAIL>>", "homepage": "https://dub.co", "repository": {"type": "git", "url": "git+https://github.com/steven-tey/dub.git"}, "bugs": {"url": "https://github.com/steven-tey/dub/issues"}, "keywords": ["dub", "dub.co", "ui"], "publishConfig": {"access": "public"}}