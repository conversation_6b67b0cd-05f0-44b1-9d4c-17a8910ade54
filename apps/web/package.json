{"name": "web", "private": true, "license": "AGPL-3.0-or-later", "scripts": {"dev": "contentlayer build && concurrently --kill-others \"contentlayer dev\" \"prisma generate && next dev --turbo --port 8888\" \"prisma generate && prisma studio --browser none\" \"email dev --port 3333\"", "build": "contentlayer build && prisma generate && next build", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "next lint", "start": "next start", "script": "tsx ./scripts/run.ts"}, "dependencies": {"@boxyhq/saml-jackson": "^1.14.2", "@dub/tailwind-config": "workspace:*", "@dub/ui": "workspace:*", "@dub/utils": "workspace:*", "@next-auth/prisma-adapter": "^1.0.5", "@planetscale/database": "^1.16.0", "@prisma/client": "^5.9.1", "@react-email/components": "^0.0.14", "@react-email/render": "^0.0.12", "@sindresorhus/slugify": "^2.2.1", "@splinetool/react-spline": "^2.2.6", "@splinetool/runtime": "^0.9.481", "@stripe/stripe-js": "^1.44.1", "@upstash/qstash": "^2.3.0", "@upstash/ratelimit": "^0.3.6", "@upstash/redis": "^1.25.1", "@vercel/analytics": "^1.0.0", "@vercel/edge": "^0.3.1", "@vercel/edge-config": "^0.4.1", "@vercel/og": "^0.6.2", "@vercel/speed-insights": "^1.0.1", "@visx/axis": "^2.14.0", "@visx/event": "^2.6.0", "@visx/geo": "^2.10.0", "@visx/gradient": "^3.3.0", "@visx/grid": "^2.12.2", "@visx/group": "^3.3.0", "@visx/responsive": "^2.10.0", "@visx/scale": "^3.3.0", "@visx/shape": "^2.12.2", "@visx/text": "^3.3.0", "@visx/tooltip": "^2.16.0", "aws4fetch": "^1.0.18", "bottleneck": "^2.19.5", "class-variance-authority": "^0.7.0", "cmdk": "^0.2.0", "cobe": "^0.6.3", "concurrently": "^8.0.1", "contentlayer": "^0.3.4", "crisp-sdk-web": "^1.0.19", "d3-array": "^3.2.4", "fast-xml-parser": "^4.3.6", "framer-motion": "^10.16.16", "fuse.js": "^6.6.2", "github-slugger": "^2.0.0", "he": "^1.2.0", "html-escaper": "^3.0.3", "js-cookie": "^3.0.5", "lucide-react": "^0.386.0", "nanoid": "^5.0.1", "next": "14.2.5", "next-auth": "^4.24.4", "next-contentlayer": "^0.3.4", "node-fetch": "^3.3.2", "node-html-parser": "^6.1.4", "nodemailer": "^6.9.3", "openapi-types": "^12.1.3", "openapi3-ts": "^4.2.1", "postmark": "^4.0.2", "prisma": "^5.9.1", "punycode": "^2.3.0", "react": "^18.2.0", "react-colorful": "^5.6.1", "react-dom": "^18.2.0", "react-dom-confetti": "^0.2.0", "react-email": "^2.0.0", "react-highlight-words": "^0.20.0", "react-medium-image-zoom": "^5.1.7", "react-parallax-tilt": "^1.7.70", "react-spring": "^9.5.5", "react-textarea-autosize": "^8.4.0", "react-tweet": "^3.1.1", "rehype-autolink-headings": "^6.1.1", "rehype-pretty-code": "^0.9.5", "rehype-slug": "^5.1.0", "remark-gfm": "^3.0.1", "shiki": "^0.14.1", "sonner": "^0.5.0", "stripe": "^12.12.0", "swr": "^2.1.5", "topojson-client": "^3.1.0", "unified": "10.1.2", "unsplash-js": "^7.0.18", "use-debounce": "^8.0.4", "vaul": "^0.6.8", "zod": "^3.22.4", "zod-error": "^1.5.0", "zod-openapi": "^2.12.0"}, "devDependencies": {"@types/dotenv-flow": "^3.3.2", "@types/he": "^1.2.3", "@types/html-escaper": "^3.0.0", "@types/ms": "^0.7.31", "@types/node": "18.11.9", "@types/react": "^18.2.47", "@types/react-dom": "^18.2.14", "@types/react-highlight-words": "^0.16.4", "@types/topojson-client": "^3.1.1", "array-to-ndjson": "^1.0.1", "autoprefixer": "^10.4.16", "dotenv-flow": "^4.0.0", "papaparse": "^5.4.1", "postcss": "^8.4.31", "postcss-import": "^15.1.0", "prettier": "^3.2.5", "prettier-plugin-organize-imports": "^3.2.4", "prettier-plugin-tailwindcss": "^0.5.11", "tailwindcss": "^3.3.3", "tsx": "^3.14.0", "turbo": "^1.10.14", "typescript": "^5.1.6"}, "browser": {"crypto": false}}